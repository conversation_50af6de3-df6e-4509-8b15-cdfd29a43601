<?php

namespace Prism\Bedrock\Schemas\Anthropic\Concerns;

trait ExtractsText
{
    /**
     * @param  array<string, mixed>  $data
     */
    protected function extractText(array $data): string
    {
        return array_reduce(
            data_get($data, 'content', []),
            function (string $text, array $content): string {
                if (data_get($content, 'type') === 'text') {
                    $text .= data_get($content, 'text');
                }

                return $text;
            },
            ''
        );
    }
}
