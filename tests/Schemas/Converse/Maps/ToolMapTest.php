<?php

declare(strict_types=1);

namespace Tests\Schemas\Converse\Maps;

use Prism\Bedrock\Schemas\Converse\Maps\ToolMap;
use Prism\Prism\Tool;

it('maps tools', function (): void {
    $tool = (new Tool)
        ->as('search')
        ->for('Searching the web')
        ->withStringParameter('query', 'the detailed search query')
        ->using(fn (): string => '[Search results]');

    expect(ToolMap::map([$tool]))->toBe([
        [
            'toolSpec' => [
                'name' => 'search',
                'description' => 'Searching the web',
                'inputSchema' => [
                    'json' => [
                        'type' => 'object',
                        'properties' => [
                            'query' => [
                                'description' => 'the detailed search query',
                                'type' => 'string',
                            ],
                        ],
                        'required' => ['query'],
                    ],
                ],
            ],
        ],
    ]);
});
