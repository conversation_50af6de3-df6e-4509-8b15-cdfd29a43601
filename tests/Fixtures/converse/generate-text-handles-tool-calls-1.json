{"metrics": {"latencyMs": 422}, "output": {"message": {"content": [{"text": "<thinking> To find out the weather in Detroit today, I will use the \"weather\" tool with the city parameter set to \"Detroit\". </thinking>\n"}, {"toolUse": {"input": {"city": "Detroit"}, "name": "weather", "toolUseId": "tooluse_fcGzeM2lSDeK8J2xouQtjQ"}}], "role": "assistant"}}, "stopReason": "tool_use", "usage": {"inputTokens": 460, "outputTokens": 60, "totalTokens": 520}}