<?php

declare(strict_types=1);

use Prism\Bedrock\Rectors\ReorderMethodsRector;
use <PERSON>\CodeQuality\Rector\Class_\InlineConstructorDefaultToPropertyRector;
use <PERSON>\Config\RectorConfig;
use <PERSON>\Set\ValueObject\LevelSetList;
use <PERSON>\Set\ValueObject\SetList;

return RectorConfig::configure()
    ->with<PERSON><PERSON><PERSON><PERSON>()
    ->withPaths([
        __DIR__.'/src',
        __DIR__.'/tests',
    ])
    ->withRules([
        InlineConstructorDefaultToPropertyRector::class,
        ReorderMethodsRector::class,
    ])
    ->withSets([
        LevelSetList::UP_TO_PHP_83,
        LevelSetList::UP_TO_PHP_82,
        SetList::CODE_QUALITY,
        SetList::DEAD_CODE,
        SetList::EARLY_RETURN,
        SetList::TYPE_DECLARATION,
        SetList::PRIVATIZATION,
    ]);
