name: PHPStan

on:
  push:
    branches:
      - main
      - "*.x"
  pull_request:

env:
  phpv: 8.2

jobs:
  phpstan:
    name: phpstan
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ env.phpv }}
          coverage: none

      - name: Install composer dependencies
        uses: ramsey/composer-install@v3

      - name: Run PHPStan
        run: ./vendor/bin/phpstan --error-format=github
