name: Tests

on:
  push:
    branches:
      - main
      - "*.x"
  pull_request:

jobs:
  test:
    runs-on: ${{ matrix.os }}
    timeout-minutes: 5
    strategy:
      fail-fast: true
      matrix:
        os: [ubuntu-latest]
        php: [8.2, 8.3, 8.4]
        laravel: [11.*, 12.*]
        stability: [prefer-lowest, prefer-stable]
        include:
          - laravel: 11.*
            testbench: 9.*
            carbon: ^2.63
          - laravel: 12.*
            testbench: 10.*
            carbon: ^3.8.4

    name: P${{ matrix.php }} - L${{ matrix.laravel }} - ${{ matrix.stability }} - ${{ matrix.os }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, iconv, imagick, fileinfo
          coverage: none

      - name: Setup problem matchers
        run: |
          echo "::add-matcher::${{ runner.tool_cache }}/php.json"
          echo "::add-matcher::${{ runner.tool_cache }}/phpunit.json"

      - name: Install dependencies
        run: |
          composer require "laravel/framework:${{ matrix.laravel }}" "orchestra/testbench:${{ matrix.testbench }}" "nesbot/carbon:${{ matrix.os == 'windows-latest' && '^^^' || '' }}${{ matrix.carbon }}" --no-interaction --no-update
          composer update --${{ matrix.stability }} --prefer-dist --no-interaction

      - name: List Installed Dependencies
        run: composer show -D

      - name: Execute tests
        run: vendor/bin/pest --ci
