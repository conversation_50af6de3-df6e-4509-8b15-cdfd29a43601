name: Formatting

on:
  push:
    branches:
      - main
      - "*.x"
  pull_request:

env:
  phpv: 8.2

jobs:
  formatting:
    name: Formatting
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ env.phpv }}
          coverage: none

      - name: Install composer dependencies
        uses: ramsey/composer-install@v3

      - name: Run Pint
        run: |
          vendor/bin/pint
          git diff --exit-code

      - name: Run Rector
        run: |
          vendor/bin/rector
          git diff --exit-code
